from mc2.cashflow.models import CcpInvoice
from mc2.institute.utils import get_institute

class Sdi:

    def __init__(self, invoice: CcpInvoice):
        self.invoice = invoice

    def get_institute_data(self):
        institute = get_institute()
        if not institute:
            raise Exception('Institute not found')
        if not institute.fiscal_code:
            raise Exception('Institute vat number not found')
        if not institute.school_fiscal_code:
            raise Exception('Institute fiscal code not found')
        if not institute.name:
            raise Exception('Institute name not found')
        if not institute.contact:
            raise Exception('Institute contact not found')
        if not institute.contact.address:
            raise Exception('Institute address not found')
        if not institute.contact.cap:
            raise Exception('Institute cap not found')
        if not institute.contact.city:
            raise Exception('Institute city not found')
        
        return dict(
            fiscal_code=institute.fiscal_code,
            school_fiscal_code=institute.school_fiscal_code,
            name=institute.name,
            address=institute.contact.address,
            cap=institute.contact.cap,
            city=institute.contact.city.description,
            province=institute.contact.city.province
        )



        
    


    