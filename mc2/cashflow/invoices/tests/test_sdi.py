from django.test import TestCase
from mc2.cashflow.invoices.sdi import Sdi
from mc2.institute.tests.factories import InstituteFactory
from mc2.common.tests.factories import ContactFactory, CitiesFactory
from mc2.cashflow.models.invoice import CcpInvoice

class TestSdi(TestCase):

    def test_get_institute_data_ok(self):
        contact = ContactFactory(
            cap='42100', 
            address='Test address 1',
            city=CitiesFactory(
                description='Test city 1',
                province='Test province 1'
            )
        )
        institute = InstituteFactory(contact=contact)
        invoice = CcpInvoice()
        sdi = Sdi(invoice)
        data = sdi.get_institute_data()
        self.assertEqual(data['fiscal_code'], institute.fiscal_code)
        self.assertEqual(data['school_fiscal_code'], institute.school_fiscal_code)
        self.assertEqual(data['name'], institute.name)
        self.assertEqual(data['address'], institute.contact.address)
        self.assertEqual(data['cap'], institute.contact.cap)
        self.assertEqual(data['city'], institute.contact.city.description)
        self.assertEqual(data['province'], institute.contact.city.province)
