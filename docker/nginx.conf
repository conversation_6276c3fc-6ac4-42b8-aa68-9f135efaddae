user  nobody;
worker_processes  1;

error_log  /dev/stderr warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" "$request_time"';

    access_log  /dev/stdout  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;

    server {
        listen 80;

        # Static files with hash (cache busting) - long cache
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\.[a-f0-9]{8,}\.
        {
            root /;
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
        }

        # Static files without hash - short cache
        location /static/
        {
            root /;
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        location /media/
        {
            root /;
            expires 1h;
            add_header Cache-Control "public, must-revalidate";
        }

        location = /favicon.ico  {
        return 404;
        access_log off;
        log_not_found off;
        }

        location ^~ /apple {
        return 404;
        access_log off;
        log_not_found off;
        }

        location = /robots.txt {
        return 404;
        access_log off;
        log_not_found off;
        }

    }
}
