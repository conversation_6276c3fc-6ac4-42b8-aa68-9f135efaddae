const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');


module.exports = (env, argv) => {
    const isProduction = argv.mode === 'production';

    return {
        mode: argv.mode || 'development',
        entry: {
            ext: './ext/ext-all.js',
            notification: './resources/js/Notification.js',
            lang: './resources/js/ext-lang-it.js',
            sencha: {
                import: './app.js',
                dependOn: 'ext'
            },
            bootstrap: './bootstrap.css',
            mc2: './src/index.js',
        },
        output: {
            filename: isProduction ? '[name].[contenthash].bundle.js' : '[name].bundle.js',
            chunkFilename: isProduction ? '[name].[contenthash].chunk.js' : '[name].chunk.js',
            assetModuleFilename: isProduction ? 'assets/[name].[contenthash][ext]' : 'assets/[name][ext]',
            path: path.resolve(__dirname, 'dist'),
            clean: true,
        },
        module: {
            rules: [
                {
                    test: /ext-all\.js$/,
                    use: [
                        {
                            loader: 'script-loader',
                        },
                    ],
                },
                {
                    test: /\.css$/,
                    use: [
                        isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
                        'css-loader'
                    ],
                },
                // Rule for images
                {
                    test: /\.(png|jpe?g|gif|svg)$/i,
                    type: 'asset/resource',
                    generator: {
                        filename: isProduction ? 'resources/[name].[contenthash][ext]' : 'resources/[name][ext]'
                    }
                },
            ],
        },
    devServer: {
        static: path.resolve(__dirname),
        port: 3000,
        open: false,
        hot: true,
        proxy: [
            {
                context: ['/mc2', '/mc2-api'],
                target: 'http://localhost',
                changeOrigin: true,
                secure: false,
            },
            {
                context: ['/next-api'],
                target: 'http://mastertest9.registroelettronico.com',
                changeOrigin: true,
                secure: false,
            },
            {
                context: ['/mc_2'],
                target: 'http://localhost:8000',
                changeOrigin: true,
                secure: false,
                pathRewrite: {
                    '^/mc_2': ''
                }
            },
        ],
    },
        plugins: [
            new HtmlWebpackPlugin({
                template: 'index.html',
                favicon: path.resolve(__dirname, 'resources', 'images', 'favicon.png'),
            }),
            new CopyWebpackPlugin({
                patterns: [
                    { from: path.resolve(__dirname, 'app'), to: 'app' },
                    {
                        from: path.resolve(__dirname, 'resources', 'images', 'background.jpg'),
                        to: isProduction ? 'resources/images/background.[contenthash].jpg' : 'resources/images/background.jpg'
                    },
                    {
                        from: path.resolve(__dirname, 'resources', 'icons', 'accept.png'),
                        to: isProduction ? 'resources/icons/accept.[contenthash].png' : 'resources/icons/accept.png'
                    },
                    {
                        from: path.resolve(__dirname, 'resources', 'icons', 'delete.png'),
                        to: isProduction ? 'resources/icons/delete.[contenthash].png' : 'resources/icons/delete.png'
                    },
                    {
                        from: path.resolve(__dirname, 'resources', 'icons', 'attach.png'),
                        to: isProduction ? 'resources/icons/attach.[contenthash].png' : 'resources/icons/attach.png'
                    },
                ]
            }),
            ...(isProduction ? [
                new MiniCssExtractPlugin({
                    filename: '[name].[contenthash].css',
                    chunkFilename: '[id].[contenthash].css',
                })
            ] : [])
        ],
    };
};